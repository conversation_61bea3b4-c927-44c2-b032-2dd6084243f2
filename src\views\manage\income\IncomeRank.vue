<!--  -->
<template>
  <div class="income-rank">
    <div class="header">
      <h2>营收/收入排行</h2>
    </div>
    
    <div class="rank-container">
      <div 
        v-for="(item, index) in rankList" 
        :key="item.id"
        class="rank-item"
        :class="{ 'rank-header': index === 0 }"
      >
        <div class="rank-badge">
          <i class="iconfont icon-r-shield"></i>
          销量第{{ index + 1 }}
        </div>
        
        <div class="rank-content">
          <div class="product-image">
            <img :src="item.image" :alt="item.name" />
          </div>
          
          <div class="product-info">
            <table class="info-table">
              <thead>
                <tr>
                  <th>商品id</th>
                  <th>商品名称</th>
                  <th>销售额</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ item.id }}</td>
                  <td class="product-name">{{ item.name }}</td>
                  <td class="sales-amount">￥{{ item.sales }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from "element-plus";

export default {
  name: "IncomeRank",

  data() {
    return {
      rankList: [
        {
          id: 2,
          name: "衬衫",
          sales: 13700.5,
          image: "https://via.placeholder.com/120x120/FFB6C1/000000?text=衬衫"
        },
        {
          id: 5,
          name: "女上衣",
          sales: 232,
          image: "https://via.placeholder.com/120x120/87CEEB/000000?text=女上衣"
        },
        {
          id: 10,
          name: "墨镜",
          sales: 180,
          image: "https://via.placeholder.com/120x120/DDA0DD/000000?text=墨镜"
        },
        {
          id: 7,
          name: "休闲鞋",
          sales: 110.4,
          image: "https://via.placeholder.com/120x120/98FB98/000000?text=休闲鞋"
        }
      ]
    };
  },

  methods: {
    async getRankData() {
      try {
        // 这里应该调用实际的API
        // const res = await apiRequest({
        //   url: "api/income/getRank",
        //   method: "get"
        // });
        // if (res.code === "200") {
        //   this.rankList = res.data;
        // }
        
        // 模拟数据已在data中定义
        console.log("排行榜数据加载完成");
      } catch (e) {
        ElMessage({
          showClose: true,
          message: e.message || "获取排行榜数据失败",
          type: "error",
          duration: 5000,
        });
      }
    }
  },

  created() {
    this.getRankData();
  },

  mounted() {
    // 组件挂载完成
  }
};
</script>

<style scoped>
.income-rank {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  color: #333;
  font-size: 24px;
  font-weight: bold;
}

.rank-container {
  max-width: 1200px;
  margin: 0 auto;
}

.rank-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.rank-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rank-badge .iconfont {
  font-size: 22px;
}

.rank-content {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 30px;
}

.product-image {
  flex-shrink: 0;
}

.product-image img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.product-info {
  flex: 1;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
}

.info-table th,
.info-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.info-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #333;
  font-size: 16px;
}

.info-table td {
  font-size: 15px;
  color: #666;
}

.product-name {
  color: #409EFF;
  font-weight: 500;
}

.sales-amount {
  color: #E6A23C;
  font-weight: bold;
  font-size: 16px;
}

/* 第一名特殊样式 */
.rank-item:first-child .rank-badge {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #333;
}

/* 第二名特殊样式 */
.rank-item:nth-child(2) .rank-badge {
  background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
}

/* 第三名特殊样式 */
.rank-item:nth-child(3) .rank-badge {
  background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rank-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .info-table {
    font-size: 14px;
  }
  
  .info-table th,
  .info-table td {
    padding: 8px 12px;
  }
}
</style>
