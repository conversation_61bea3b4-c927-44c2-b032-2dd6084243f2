<template>
  <el-menu :default-openeds="['2', 'good']" style="height: 100%;"
           background-color="rgb(48,65,86)"
           text-color ="#fff"
           :collapse-transition="false"
           :collapse="isCollapse"
           router
  >
    <div style="height: 60px; margin-left: 30px; line-height: 60px">
  <router-link to="/manage/home">
    <img src="../resource/logo.png" style="width: 32px; position: relative; top: 7px; right: 6px" />
  </router-link>
  <!-- 修复：span 上的 slot="title" 改为 #title 模板 -->
  <template v-if="!isCollapse">
    <span style="color: aliceblue; font-size: 20px">在线商城后台管理</span>
  </template>
</div>

<el-menu-item index="/manage/home" class="el-item-menu" style="font-size: 16px">
  <i class="iconfont icon-r-home" style="font-size: 24px; color: white"></i>
  <!-- 修复：span slot="title" → 直接写文本或用 template #title -->
  <template #title> 主页</template>
</el-menu-item>

<el-menu-item index="/" class="el-item-menu" style="font-size: 16px">
  <i class="iconfont icon-r-mark1" style="font-size: 24px; color: white"></i>
  <template #title> 前台</template>
</el-menu-item>

<!-- 一级子菜单：系统管理 -->
<el-submenu index="2" style="font-size: 16px">
  <!-- 修复：template slot="title" → v-slot:title 或 #title -->
  <template #title>
    <i class="iconfont icon-r-setting" style="font-size: 24px; color: white"></i>
    <span> 系统管理</span>
  </template>

  <!-- 用户管理 -->
  <!-- <el-submenu v-show="userGroup" index="user" class="el-item-menu"> -->
  <el-submenu index="user" class="el-item-menu">
    <template #title>
      <i class="iconfont icon-r-user2" style="font-size: 24px; color: white"></i>
      <span> 用户管理</span>
    </template>
    <!-- <el-menu-item index="/manage/user" v-if="menuFlags.userMenu"> 用户管理</el-menu-item> -->
     <el-menu-item index="/manage/user"> 用户管理</el-menu-item>
  </el-submenu>

  <!-- 文件管理 -->
  <!-- <el-submenu v-if="fileGroup" index="file" class="el-item-menu" style="font-size: 18px"> -->
  <el-submenu  index="file" class="el-item-menu" style="font-size: 18px">
    <template #title>
      <i class="iconfont icon-r-paper" style="font-size: 24px; color: white"></i>
      <span> 文件管理</span>
    </template>
    <!-- <el-menu-item index="/manage/file" v-if="menuFlags.fileMenu">文件管理</el-menu-item> -->
    <el-menu-item index="/manage/file" >文件管理</el-menu-item>
    <!-- <el-menu-item index="/manage/avatar" v-if="menuFlags.avatarMenu">头像管理</el-menu-item> -->
    <el-menu-item index="/manage/avatar" >头像管理</el-menu-item>
  </el-submenu>

  <!-- 商品管理 -->
  <!-- <el-submenu v-if="GoodGroup" index="good" class="el-item-menu" style="font-size: 18px"> -->
  <el-submenu  index="good" class="el-item-menu" style="font-size: 18px">
    <template #title>
      <i class="iconfont icon-r-find" style="font-size: 24px; color: white"></i>
      <span> 商品管理</span>
    </template>
    <el-menu-item index="/manage/category" v-if="menuFlags.categoryMenu">商品分类管理</el-menu-item>
    <!-- <el-menu-item index="/manage/carousel" v-if="menuFlags.carouselMenu">轮播图管理</el-menu-item> -->
    <el-menu-item index="/manage/carousel" >轮播图管理</el-menu-item>
    <el-menu-item index="/manage/good" v-if="menuFlags.goodMenu">商品管理</el-menu-item>
    <!-- <el-menu-item index="/manage/order" v-if="menuFlags.orderMenu">订单管理</el-menu-item> -->
    <el-menu-item index="/manage/order">订单管理</el-menu-item>
  </el-submenu>

  <!-- 营收管理 -->
  <!-- <el-submenu v-if="incomeGroup" index="income" class="el-item-menu" style="font-size: 18px"> -->
    <el-submenu  index="income" class="el-item-menu" style="font-size: 18px">
    <template #title>
      <i class="iconfont icon-r-shield" style="font-size: 24px; color: white"></i>
      <span> 营收管理</span>
    </template>
    <el-menu-item index="/manage/incomeChart">图表分析</el-menu-item>
    <el-menu-item index="/manage/incomeRank">收入排行榜</el-menu-item>
    <!-- <el-menu-item index="/manage/incomeChart" v-if="menuFlags.incomeChartMenu">图表分析</el-menu-item> -->
    <!-- <el-menu-item index="/manage/incomeRank" v-if="menuFlags.incomeRankMenu">收入排行榜</el-menu-item> -->
  </el-submenu>
</el-submenu>
  </el-menu>
</template>

<script>
//import request from "@/utils/request";


export default {
  name: "Aside",
  props: {
    isCollapse: Boolean,
  },
  data() {
    return{
      role : 'user',
      menuFlags: {
        userMenu: false,
        fileMenu: false,
        avatarMenu: false,
        goodMenu: false,
        carouselMenu: false,
        orderMenu: false,
        categoryMenu: false,
        incomeChartMenu: false,
        incomeRankMenu: false,
      }
    }
  },
  computed: {
    userGroup: function (){
      return this.menuFlags.userMenu
    },
    fileGroup: function (){
      return this.menuFlags.fileMenu || this.menuFlags.avatarMenu
    },
    GoodGroup: function (){
      return this.menuFlags.goodMenu ||this.menuFlags.orderMenu || this.menuFlags.categoryMenu || this.menuFlags.carouselMenu
    },
    incomeGroup: function () {
      return this.menuFlags.incomeChartMenu || this.menuFlags.incomeRankMenu
    }
  },
  mounted() {
    
  },
  created() {
    // request.post("http://localhost:9197/role").then(res=>{
    //   if(res.code==='200'){
    //     this.role = res.data;
    //     console.log("asider，role："+this.role)
    //     if(this.role === 'admin'){
    //       this.menuFlags.userMenu = true
    //       this.menuFlags.fileMenu = true
    //       this.menuFlags.avatarMenu = true
    //       this.menuFlags.categoryMenu = true
    //       this.menuFlags.goodMenu = true
    //       this.menuFlags.carouselMenu = true
    //       this.menuFlags.orderMenu = true
    //       this.menuFlags.incomeChartMenu = true
    //       this.menuFlags.incomeRankMenu = true
    //     }
    //     else if(this.role==='user'){

    //     }
    //     console.log(this.menuFlags)
    //   }
    // })
  }
}
</script>

<style scoped>

</style>