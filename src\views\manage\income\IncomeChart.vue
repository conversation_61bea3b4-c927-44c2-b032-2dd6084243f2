<!--  -->
<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <div
        id="chartSum"
        style="
          display: inline-block;
          margin-left: 50px;
          margin-top: 30px;
          font-weight: bold;
          font-size: 22px;
          color: #ffb02a;
          border: 1px lightgrey solid;
          border-radius: 10px;
          padding: 20px;
        "
      >
        总计：￥{{ total}}
      </div>

      <!--  本周收入柱状图-->
      <el-tab-pane label="本周收入" name="weekBar">
        <div
          id="weekBar"
          style="width: 1200px; height: 500px; margin: auto auto"
        ></div>
      </el-tab-pane>

      <!-- 本月收入折线图-->
      <el-tab-pane label="本月收入" name="monthLine">
        <div
          id="monthLine"
          style="width: 1200px; height: 500px; margin: 10px auto"
        ></div>
      </el-tab-pane>

      <!--      各类收入柱状图-->
      <el-tab-pane label="各类收入柱状图" name="categoryBar">
        <div
          id="categoryBar"
          style="width: 1200px; height: 500px; margin: auto auto"
        ></div>
      </el-tab-pane>

      <!--      收入结构饼图-->
      <el-tab-pane label="收入结构饼图" name="pie">
        <div
          id="pie"
          style="width: 600px; height: 600px; margin: 10px auto"
        ></div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import * as echarts from "echarts";
// import { apiRequest } from "@/utils/request";
import { ElMessage } from "element-plus";
export default {
  name: "IncomeChart", // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {},

  // 数据区
  data() {
    return {
      activeName: "weekBar",
      sumIncome: 0,
      categoryIncomes: [],
      categoryNames: [],
      incomes: [],
      totalAll: 0,
      totalWeek: 0,
      totalMonth: 0,
      total: 0,
      // 本周数据
      weekData: {
        days: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        incomes: [0, 0, 0, 0, 0, 0, 0]
      },
      // 本月数据
      monthData: {
        days: [],
        incomes: []
      }
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    handleClick(tab) {
      switch (tab.name) {
        case "weekBar":
          this.total = this.totalWeek;
          this.getWeekChart();
          break;
        case "monthLine":
          this.total = this.totalMonth;
          this.getMonthChart();
          break;
        case "categoryBar":
          this.total = this.totalAll;
          this.getCategoryChart();
          break;
        case "pie":
          this.total = this.totalAll;
          this.getPieChart();
          break;
      }
    },

    // 本周收入柱状图
    async getWeekChart() {
      var weekChart = echarts.init(document.getElementById("weekBar"));
      var weekOption = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        title: {
          text: "本周收入",
          x: "center",
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: this.weekData.days,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: "value",
          name: '收入(元)'
        },
        series: [
          {
            name: '收入',
            type: "bar",
            barWidth: '60%',
            data: this.weekData.incomes,
            itemStyle: {
              color: '#409EFF'
            },
            label: {
              show: true,
              position: 'top'
            }
          }
        ]
      };

      try {
        // 模拟数据，实际应该从后端获取
        this.weekData.incomes = [1200, 800, 1500, 900, 1100, 1300, 1000];
        let weekTotal = this.weekData.incomes.reduce((sum, val) => sum + val, 0);
        this.totalWeek = weekTotal;
        this.total = weekTotal;

        weekOption.series[0].data = this.weekData.incomes;
        weekChart.setOption(weekOption);
      } catch (e) {
        ElMessage({
          showClose: true,
          message: e.message || "获取本周数据失败",
          type: "error",
          duration: 5000,
        });
      }
    },

    // 商品类别柱状图数据
    async getCategoryChart() {
      var barChart = echarts.init(document.getElementById("categoryBar"));
      var barOption = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        title: {
          text: "各类收入柱状图",
          x: "center",
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: [],
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: "value",
          name: '收入(元)'
        },
        series: [
          {
            name: '收入',
            data: [],
            type: "bar",
            barWidth: '60%',
            itemStyle: {
              color: '#67C23A'
            },
            label: {
              show: true,
              position: "top",
            }
          },
        ],
      };

      try {
        // 模拟数据，实际应该从后端获取
        const mockData = {
          categoryNames: ['服装', '电子', '图书', '家居', '运动', '美妆'],
          incomes: [15000, 12000, 8000, 10000, 6000, 9000]
        };

        barOption.xAxis.data = mockData.categoryNames;
        barOption.series[0].data = mockData.incomes;
        barChart.setOption(barOption);

        //计算总和
        let sum = mockData.incomes.reduce((total, val) => total + val, 0);
        this.total = sum;
        this.totalAll = sum;

      } catch (e) {
        ElMessage({
          showClose: true,
          message: e.message || "获取分类数据失败",
          type: "error",
          duration: 5000,
        });
      }
    },

    // 本月收入折线图
    async getMonthChart() {
      var monthChart = echarts.init(document.getElementById("monthLine"));

      // 生成本月日期
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const daysInMonth = new Date(year, month, 0).getDate();

      const days = [];
      const incomes = [];

      for (let i = 1; i <= daysInMonth; i++) {
        days.push(`${month}-${i.toString().padStart(2, '0')}`);
        // 模拟数据
        incomes.push(Math.floor(Math.random() * 2000) + 500);
      }

      this.monthData.days = days;
      this.monthData.incomes = incomes;

      var monthOption = {
        tooltip: {
          trigger: "axis"
        },
        title: {
          text: "本月收入",
          x: "center",
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: days,
          boundaryGap: false
        },
        yAxis: {
          type: "value",
          name: '收入(元)'
        },
        series: [
          {
            name: '收入',
            type: "line",
            data: incomes,
            smooth: true,
            itemStyle: {
              color: '#E6A23C'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(230, 162, 60, 0.3)'
                }, {
                  offset: 1, color: 'rgba(230, 162, 60, 0.1)'
                }]
              }
            }
          }
        ]
      };

      try {
        let monthTotal = incomes.reduce((sum, val) => sum + val, 0);
        this.totalMonth = monthTotal;
        this.total = monthTotal;

        monthChart.setOption(monthOption);
      } catch (e) {
        ElMessage({
          showClose: true,
          message: e.message || "获取本月数据失败",
          type: "error",
          duration: 5000,
        });
      }
    },

    // 收入结构饼图
    async getPieChart() {
      var pieChart = echarts.init(document.getElementById("pie"));

      const pieData = [
        { value: 35000, name: '男装' },
        { value: 28000, name: '女装' },
        { value: 15000, name: '童装' },
        { value: 12000, name: '配饰' }
      ];

      var pieOption = {
        tooltip: {
          trigger: "item",
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
          text: "收入结构饼图",
          x: "center",
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: pieData.map(item => item.name)
        },
        series: [
          {
            name: '收入分布',
            type: 'pie',
            radius: '50%',
            data: pieData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {d}%'
            }
          }
        ]
      };

      try {
        let pieTotal = pieData.reduce((sum, item) => sum + item.value, 0);
        this.total = pieTotal;
        this.totalAll = pieTotal;

        pieChart.setOption(pieOption);
      } catch (e) {
        ElMessage({
          showClose: true,
          message: e.message || "获取饼图数据失败",
          type: "error",
          duration: 5000,
        });
      }
    }
  },

  // 生命周期 - 创建完成
  created() {
    //
  },

  // 生命周期 - 挂载完成
  mounted() {
    // 默认加载本周收入图表
    this.$nextTick(() => {
      this.getWeekChart();
    });
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    //
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    //
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {},
};
</script>

<style scoped>
</style>